<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hengjian-business</artifactId>
        <groupId>com.hengjian</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zsmall.extend</groupId>
    <artifactId>zsmall-extend</artifactId>
    <version>${zsmall.version}</version>
    <packaging>pom</packaging>
    <name>ZS-Mall扩展模块pom</name>
    <description>
        zsmall-extend 扩展模块
    </description>

    <modules>
        <module>zsmall-extend-core</module>
        <module>zsmall-thirdparty-shop</module>
        <module>zsmall-thirdparty-wms</module>
        <module>zsmall-logistics</module>
        <module>zsmall-payment</module>
        <module>zsmall-extend-event</module>
        <module>zsmall-extend-pdf</module>
        <module>zsmall-extend-es</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
