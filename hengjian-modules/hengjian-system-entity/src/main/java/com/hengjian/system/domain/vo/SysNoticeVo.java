package com.hengjian.system.domain.vo;

import com.hengjian.common.translation.annotation.Translation;
import com.hengjian.common.translation.constant.TransConstant;
import com.hengjian.system.domain.SysNotice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 通知公告视图对象 sys_notice
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysNotice.class)
public class SysNoticeVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 公告ID
     */
    private Long noticeId;

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公告类型（1通知 2公告）
     */
    private String noticeType;

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 公告状态（0正常 1关闭）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 通知范围（通知的租户类型）
     */
//    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tenantTypes;

    /**
     * 已读状态
     */
    private Boolean readed;

    /**
     * 头像存储桶Id
     */
    private Long imageOssId;

    /**
     * 头像存储路径
     */
    private String imageSavePath;

    /**
     * 头像展示地址
     */
    private String imageShowUrl;

}
